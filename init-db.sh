#!/bin/bash

# BMS-BE Database Initialization Script with <PERSON>rror Checking
# =========================================================
#
# This script uses psql command-line tools for database operations.
# Alternative: If you prefer not to use psql, you can manually execute
# the init-db-simple.sql file using your preferred database management tool
# (e.g., pgAdmin, DBeaver, etc.)
#
# The script now uses simple coordinates instead of PostGIS extensions.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database connection parameters
DB_NAME="bms_db"
DB_USER="dbuser"
DB_PASSWORD="dbheslo"
DB_HOST="localhost"
DB_PORT="5432"
SQL_FILE="init-db-simple.sql"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if PostgreSQL is accessible
check_postgresql() {
    print_status "Checking PostgreSQL connectivity..."

    if ! command -v psql &> /dev/null; then
        print_error "PostgreSQL command line tools (psql) not found"
        print_error "Please install PostgreSQL client tools or use a database management tool"
        print_error "Alternative: Execute the SQL file manually using your preferred database client"
        exit 1
    fi
    
    # Test connection
    if ! PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to PostgreSQL server"
        print_error "Please check if PostgreSQL is running and credentials are correct"
        print_error "Host: $DB_HOST:$DB_PORT, User: $DB_USER"
        exit 1
    fi
    
    print_success "PostgreSQL connection successful"
}

# Function to check if SQL file exists and is readable
check_sql_file() {
    print_status "Checking SQL file: $SQL_FILE"
    
    if [ ! -f "$SQL_FILE" ]; then
        print_error "SQL file '$SQL_FILE' not found"
        print_error "Please make sure the file exists in the current directory"
        exit 1
    fi
    
    if [ ! -r "$SQL_FILE" ]; then
        print_error "SQL file '$SQL_FILE' is not readable"
        exit 1
    fi
    
    print_success "SQL file found and readable"
}

# Function to validate SQL syntax
validate_sql_syntax() {
    print_status "Validating SQL syntax..."
    
    # Create a temporary file for syntax checking
    TEMP_SQL=$(mktemp)
    
    # Remove \connect commands for syntax validation
    grep -v "^\\\\connect" "$SQL_FILE" > "$TEMP_SQL"
    
    # Check syntax by doing a dry run
    if ! PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres \
         --set ON_ERROR_STOP=1 --set AUTOCOMMIT=off --single-transaction \
         -f "$TEMP_SQL" --dry-run &> /dev/null 2>&1; then
        
        print_warning "SQL syntax validation completed (some errors may be expected for missing database)"
    else
        print_success "SQL syntax appears valid"
    fi
    
    rm -f "$TEMP_SQL"
}

# Function to check for unknown data types
check_data_types() {
    print_status "Checking for unknown data types..."

    # List of PostgreSQL built-in types (PostGIS types removed)
    KNOWN_TYPES=(
        "UUID" "VARCHAR" "INTEGER" "BOOLEAN" "TIMESTAMP" "JSONB" "DECIMAL" "ENUM"
    )

    # Extract data types from SQL file
    FOUND_TYPES=$(grep -i -E "(CREATE TYPE|::|VARCHAR\(|INTEGER|BOOLEAN|TIMESTAMP|JSONB|UUID|DECIMAL)" "$SQL_FILE" | \
                  sed -E 's/.*\b(UUID|VARCHAR|INTEGER|BOOLEAN|TIMESTAMP|JSONB|DECIMAL)\b.*/\1/gi' | \
                  sort -u)

    print_status "Data types found in SQL file:"
    echo "$FOUND_TYPES" | while read -r type; do
        if [ -n "$type" ]; then
            echo "  - $type"
        fi
    done
}

# Function to check basic PostgreSQL features
check_postgresql_features() {
    print_status "Checking PostgreSQL features..."

    # Check if JSONB is supported (PostgreSQL 9.4+)
    JSONB_AVAILABLE=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres \
                     -tAc "SELECT 1 WHERE EXISTS (SELECT 1 FROM pg_type WHERE typname = 'jsonb');")

    if [ "$JSONB_AVAILABLE" = "1" ]; then
        print_success "JSONB support is available"
    else
        print_warning "JSONB support is not available (requires PostgreSQL 9.4+)"
    fi

    print_success "Using simple coordinate storage instead of PostGIS"
}

# Function to check if database exists
check_database_exists() {
    print_status "Checking if database '$DB_NAME' exists..."
    
    DB_EXISTS=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres \
               -tAc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME';")
    
    if [ "$DB_EXISTS" = "1" ]; then
        print_warning "Database '$DB_NAME' already exists"
        echo -n "Do you want to recreate it? This will delete all existing data! (y/N): "
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            print_status "Dropping existing database..."
            PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres \
                                   -c "DROP DATABASE IF EXISTS $DB_NAME;"
            print_success "Database dropped"
            return 1  # Database needs to be created
        else
            print_status "Keeping existing database"
            return 0  # Database exists and should be kept
        fi
    else
        print_status "Database '$DB_NAME' does not exist"
        return 1  # Database needs to be created
    fi
}

# Function to execute SQL file
execute_sql() {
    print_status "Executing SQL file..."
    
    # Execute the SQL file with error handling
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres \
       --set ON_ERROR_STOP=1 -f "$SQL_FILE" > /tmp/sql_output.log 2>&1; then
        print_success "SQL file executed successfully"
        
        # Show summary of what was created
        print_status "Database initialization summary:"
        grep -i "CREATE\|INSERT" /tmp/sql_output.log | head -10
        
    else
        print_error "SQL execution failed"
        print_error "Error details:"
        cat /tmp/sql_output.log
        exit 1
    fi
}

# Function to verify database structure
verify_database() {
    print_status "Verifying database structure..."
    
    # Check if tables were created
    TABLES=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
            -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
    
    print_status "Number of tables created: $TABLES"
    
    # List tables
    print_status "Tables in database:"
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
                           -tAc "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;" | \
    while read -r table; do
        if [ -n "$table" ]; then
            echo "  - $table"
        fi
    done
    
    # Check sample data
    PROVIDER_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
                    -tAc "SELECT COUNT(*) FROM providers;" 2>/dev/null || echo "0")
    print_status "Sample providers inserted: $PROVIDER_COUNT"
    
    USER_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME \
                -tAc "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "0")
    print_status "Sample users inserted: $USER_COUNT"
}

# Main execution
main() {
    echo "BMS-BE Database Initialization Script"
    echo "====================================="
    echo ""
    
    # Run all checks
    check_postgresql
    check_sql_file
    validate_sql_syntax
    check_data_types
    check_postgresql_features
    
    # Check if database exists and handle accordingly
    if ! check_database_exists; then
        # Database needs to be created, execute SQL
        execute_sql
        verify_database
    else
        print_status "Using existing database"
        verify_database
    fi
    
    echo ""
    print_success "Database initialization completed successfully!"
    print_status "You can now start the BMS-BE application"
    
    # Clean up
    rm -f /tmp/sql_output.log
}

# Run main function
main "$@"
